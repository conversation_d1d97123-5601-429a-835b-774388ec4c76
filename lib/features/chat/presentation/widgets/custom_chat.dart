import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/list_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_subtype.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_input_controller.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/services/chat_media_service.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/chat/chat_bottom_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/chat/chat_bubble.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/chat/media_option.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/image_message_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/invite_room_message_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/video_message_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/voice_call_message_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/voice_message_widget.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart'
    as file_service;
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:super_context_menu/super_context_menu.dart';

class CustomChat extends ConsumerStatefulWidget {
  const CustomChat({
    super.key,
    required this.conversationId,
    required this.messages,
    required this.user,
    required this.typingUsers,
    required this.onSendText,
    required this.onSendImage,
    required this.onSendAudio,
    required this.onSendVideo,
  });

  final String conversationId;
  final List<types.Message> messages;
  final types.User user;
  final List<types.User> typingUsers;
  final void Function(String) onSendText;
  final void Function(file_service.FileMetadata) onSendImage;
  final void Function(file_service.FileMetadata, Duration) onSendAudio;
  final void Function(file_service.FileMetadata, Duration) onSendVideo;

  @override
  ConsumerState<CustomChat> createState() => _CustomChatState();
}

class _CustomChatState extends ConsumerState<CustomChat>
    with SingleTickerProviderStateMixin {
  // 控制器和服务
  final ScrollController _scrollController = ScrollController();
  final ChatMediaService _mediaService = ChatMediaService();
  final RefreshController _refreshController = RefreshController();

  // 状态变量
  bool _isRecordingMode = false;
  bool _isAttachmentOptionsVisible = false;
  bool _isProcessingMedia = false;
  bool _hasMore = true;

  // 分组时间间隔（毫秒）
  static const int _timeGroupInterval = 5 * 60 * 1000; // 5分钟

  // 媒体选项列表
  late List<MediaOption> _mediaOptions;

  @override
  void initState() {
    super.initState();
    
    // 初始化媒体选项
    _initMediaOptions();
    
    // 添加滚动监听
    _scrollController.addListener(_handleScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  /// 初始化媒体选项
  void _initMediaOptions() {
    _mediaOptions = [
      MediaOption(
        icon: Icons.image_outlined,
        label: '图片',
        color: Colors.green.shade600,
        onTap: _handlePickImage,
      ),
      MediaOption(
        icon: Icons.videocam_outlined,
        label: '视频',
        color: Colors.red.shade600,
        onTap: _handlePickVideo,
      ),
    ];
  }

  /// 处理滚动事件，用于隐藏附件选项和优化性能
  void _handleScroll() {
    // 隐藏附件选项
    if (_isAttachmentOptionsVisible) {
      _hideAttachmentOptions();
    }
  }

  @override
  Widget build(BuildContext context) {
    final inputController =
        ref.watch(chatInputControllerProvider(widget.conversationId));
    final maxTextLength =
        ref.watch(coreServiceProvider).config.chatConfigResp.maxContentLength;

    return GestureDetector(
      onTap: _hideAttachmentOptions,
      child: KeyboardDismissOnTap(
        child: Column(
          children: [
            // 聊天消息列表
            Expanded(
              child: _buildChatList(),
            ),

            // 打字指示器
            if (widget.typingUsers.isNotEmpty) _buildTypingIndicator(),

            // 底部输入区域
            ChatBottomWidget(
              conversationId: widget.conversationId,
              isRecordingMode: _isRecordingMode,
              isAttachmentOptionsVisible: _isAttachmentOptionsVisible,
              isProcessingMedia: _isProcessingMedia,
              mediaOptions: _mediaOptions,
              onToggleAttachmentOptions: _toggleAttachmentOptions,
              onToggleRecordingMode: (value) {
                setState(() => _isRecordingMode = value);
              },
              onSendText: () => _handleSendText(inputController),
              onAudioRecordComplete: _handleAudioRecordComplete,
              maxTextLength: maxTextLength,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建聊天列表
  Widget _buildChatList() {
    // 准备消息列表，并按时间排序（从新到旧）
    final sortedMessages = widget.messages.toList();

    if (sortedMessages.isEmpty) {
      return SmartRefresher(
        controller: _refreshController,
        reverse: true,
        enablePullDown: false,
        enablePullUp: _hasMore,
        onLoading: _onLoading,
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.idle) {
              body = const Text("Load more");
            } else if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("Load failed, click to retry");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("Release to load more");
            } else {
              body = const Text("No more data");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        child: const Center(child: Text('No messages')),
      );
    }

    // 生成包含日期标头和消息的列表项
    final listItems = <_ChatListItem>[];
    int? lastTimeGroup;

    for (int i = 0; i < sortedMessages.reversed.length; i++) {
      final message = sortedMessages.reversed.toList()[i];
      final messageTime = message.createdAt ?? 0;

      // 计算时间组（向下取整到5分钟间隔）
      final timeGroup =
          (messageTime ~/ _timeGroupInterval) * _timeGroupInterval;

      // 如果是新的时间组，添加时间标头
      if (lastTimeGroup == null || lastTimeGroup != timeGroup) {
        listItems.add(_ChatListItem(
          type: _ChatListItemType.dateHeader,
          message: message,
          timestamp: messageTime,
        ));
        lastTimeGroup = timeGroup;
      }

      // 检查是否与下一条消息是同一组
      final isNextMessageInGroup = i < sortedMessages.length - 1 &&
          sortedMessages[i + 1].author.id == message.author.id &&
          _isWithinTimeWindow(message, sortedMessages[i + 1]);

      // 添加消息
      listItems.add(_ChatListItem(
        type: _ChatListItemType.message,
        message: message,
        isNextInGroup: isNextMessageInGroup,
      ));
    }

    return SmartRefresher(
      controller: _refreshController,
      reverse: true,
      enablePullDown: false,
      enablePullUp: _hasMore,
      onLoading: _onLoading,
      header: const WaterDropHeader(),
      footer: CustomFooter(
        builder: (BuildContext context, LoadStatus? mode) {
          Widget body;
          if (mode == LoadStatus.idle) {
            body = const Text("Pull up to load more");
          } else if (mode == LoadStatus.loading) {
            body = const CupertinoActivityIndicator();
          } else if (mode == LoadStatus.failed) {
            body = const Text("Load failed, click to retry");
          } else if (mode == LoadStatus.canLoading) {
            body = const Text("Release to load more");
          } else {
            body = const Text("No more data");
          }
          return SizedBox(
            height: 55.0,
            child: Center(child: body),
          );
        },
      ),
      child: ListView.builder(
        controller: _scrollController,
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        reverse: true,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        itemCount: listItems.length,
        itemBuilder: (context, index) {
          final item = listItems.reversed.toList()[index]; // 正常顺序访问

          if (item.type == _ChatListItemType.dateHeader) {
            return _buildDateHeader(item.message);
          } else {
            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 4.h,
              ),
              child: Column(
                crossAxisAlignment: _isSelfMessage(item.message)
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                children: [
                  _buildMessageWidget(item.message, item.isNextInGroup),
                  // 为非系统消息且自己发送的消息显示状态
                  if (_isSelfMessage(item.message) &&
                      item.message.type != types.MessageType.system)
                    _buildMessageStatus(item.message),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  /// 上拉加载更多回调
  Future<void> _onLoading() async {
    try {
      final hasMore = await ref
          .read(chatProvider(widget.conversationId).notifier)
          .loadMore();
      setState(() {
        _hasMore = hasMore;
      });
      _refreshController.loadComplete();
    } catch (e) {
      _refreshController.loadFailed();
      LogUtils.e('Error loading more messages: $e', tag: 'CustomChat');
    }
  }

  /// 判断是否为自己发送的消息
  bool _isSelfMessage(types.Message message) {
    return message.author.id == widget.user.id;
  }

  /// 构建消息状态指示器
  Widget _buildMessageStatus(types.Message message) {
    // 只为自己发送的消息显示状态，且不为系统消息
    if (!_isSelfMessage(message) || message.type == types.MessageType.system) {
      return const SizedBox.shrink();
    }

    // 构建不同状态的指示器
    Widget statusWidget;
    switch (message.status) {
      case types.Status.sending:
        statusWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 10.w,
              height: 10.w,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(
                  context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              ),
            ),
            SizedBox(width: 4.w),
            Text(
              'Sending',
              style: TextStyle(
                fontSize: 10.sp,
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        );
        break;
      case types.Status.sent:
        statusWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check,
              size: 12.w,
              color: context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(width: 4.w),
            Text(
              'Sent',
              style: TextStyle(
                fontSize: 10.sp,
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        );
        break;
      case types.Status.delivered:
        statusWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check,
              size: 12.w,
              color: context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(width: 4.w),
            Text(
              'Delivered',
              style: TextStyle(
                fontSize: 10.sp,
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        );
        break;
      case types.Status.seen:
        statusWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.done_all,
              size: 12.w,
              color: Colors.blue,
            ),
            SizedBox(width: 4.w),
            Text(
              'Read',
              style: TextStyle(
                fontSize: 10.sp,
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        );
        break;
      case types.Status.error:
        statusWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 12.w,
              color: Colors.red,
            ),
            SizedBox(width: 4.w),
            Text(
              'Failed to send',
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.red,
              ),
            ),
          ],
        );
        break;
      default:
        return const SizedBox.shrink();
    }

    // 包装状态指示器
    return Padding(
      padding: EdgeInsets.only(top: 2.h, right: 12.w),
      child: GestureDetector(
        onTap: message.status == types.Status.error
            ? () => _onMessageStatusTap(context, message)
            : null,
        child: statusWidget,
      ),
    );
  }

  /// 判断两条消息是否在指定时间窗口内（用于分组）
  bool _isWithinTimeWindow(types.Message message1, types.Message message2) {
    if (message1.createdAt == null || message2.createdAt == null) return false;
    return (message1.createdAt! - message2.createdAt!).abs() <
        _timeGroupInterval;
  }

  /// 获取消息日期
  DateTime _getMessageDate(types.Message message) {
    return message.createdAt != null
        ? DateTime.fromMillisecondsSinceEpoch(message.createdAt!)
        : DateTime.now();
  }

  /// 判断两个日期是否是同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 构建日期标头
  Widget _buildDateHeader(types.Message message) {
    final messageDate = _getMessageDate(message);
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));

    // 显示详细的时间格式
    String dateText;
    String timeText = DateFormat('HH:mm').format(messageDate);

    if (_isSameDay(messageDate, now)) {
      dateText = '今天 $timeText';
    } else if (_isSameDay(messageDate, yesterday)) {
      dateText = '昨天 $timeText';
    } else {
      // 不同年份显示完整日期，否则只显示月日
      if (messageDate.year != now.year) {
        dateText = DateFormat('yyyy年MM月dd日 HH:mm').format(messageDate);
      } else {
        dateText = DateFormat('MM月dd日 HH:mm').format(messageDate);
      }
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: context.theme.colorScheme.surface.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: context.theme.colorScheme.onSurface.withValues(alpha: 0.1),
            ),
          ),
          child: Text(
            dateText,
            style: TextStyle(
              color: context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontSize: 11.sp,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建单个消息组件
  Widget _buildMessageWidget(types.Message message, bool isNextMessageInGroup) {
    // 系统消息单独处理
    if (message.type == types.MessageType.system) {
      return _buildSystemMessage(message as types.SystemMessage);
    }

    Widget messageContent;

    // 根据消息类型构建不同的内容
    switch (message.type) {
      case types.MessageType.text:
        messageContent = _buildTextMessage(message as types.TextMessage);
        break;
      case types.MessageType.image:
        messageContent = _buildImageMessage(message as types.ImageMessage);
        break;
      case types.MessageType.audio:
        messageContent = _buildAudioMessage(message as types.AudioMessage);
        break;
      case types.MessageType.video:
        messageContent = _buildVideoMessage(message as types.VideoMessage);
        break;
      default:
        messageContent = const SizedBox.shrink();
    }

    if (message.type == types.MessageType.custom) {
      final subtype = MessageSubtype.values.firstWhereOrNull((e) {
        return e.type == int.tryParse(message.metadata?['subtype'] ?? '');
      });
      switch (subtype) {
        case MessageSubtype.VOICE_CALL:
          messageContent =
              _buildVoiceCallMessage(message as types.CustomMessage);
        case MessageSubtype.INVITE_ROOM:
          return _buildInviteRoomMessage(message as types.CustomMessage);
        default:
          return const SizedBox.shrink();
      }
    }

    // 用气泡包装消息内容
    return ChatBubble(
      message: message,
      menu: _buildContextMenu(message),
      nextMessageInGroup: isNextMessageInGroup,
      currentUser: widget.user,
      onMessageStatusTap: _onMessageStatusTap,
      child: messageContent,
    );
  }
  
  /// 构建系统消息
  Widget _buildSystemMessage(types.SystemMessage message) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 40.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: context.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: context.theme.colorScheme.onSurface.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 14.sp,
            color: context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(width: 8.w),
          Flexible(
            child: Text(
              message.text,
              style: TextStyle(
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 12.sp,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建文本消息
  Widget _buildTextMessage(types.TextMessage message) {
    final isFromMe = message.author.id == widget.user.id;
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h)
          .copyWith(right: 5.w),
      child: Text(
        message.text,
        style: context.textTheme.bodyMedium?.copyWith(
          color: isFromMe
              ? context.colorScheme.onPrimary
              : context.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildImageMessage(types.ImageMessage message) {
    return ImageMessageWidget(
      message: message,
      conversationId: widget.conversationId,
    );
  }

  Widget _buildAudioMessage(types.AudioMessage message) {
    final isFromMe = message.author.id == widget.user.id;
    return VoiceMessageWidget(
      message: message,
      conversationId: widget.conversationId,
      isFromMe: isFromMe,
    );
  }

  Widget _buildVideoMessage(types.VideoMessage message) {
    return VideoMessageWidget(
      message: message,
      conversationId: widget.conversationId,
    );
  }

  Widget _buildVoiceCallMessage(types.CustomMessage message) {
    final isFromMe = message.author.id == widget.user.id;
    return VoiceCallMessageWidget(
      message: message,
      conversationId: widget.conversationId,
      isFromMe: isFromMe,
    );
  }

  Widget _buildInviteRoomMessage(types.CustomMessage message) {
    return InviteRoomMessageWidget(
      message: message,
      conversationId: widget.conversationId,
    );
  }

  /// 构建打字指示器
  Widget _buildTypingIndicator() {
    if (widget.typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    final names = widget.typingUsers.map((user) => user.firstName).join(', ');

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      alignment: Alignment.centerLeft,
      color: context.theme.colorScheme.surface,
      child: Row(
        children: [
          _buildTypingAnimation(),
          8.horizontalSpace,
          Expanded(
            child: Text(
              '$names 正在输入...',
              style: TextStyle(
                fontSize: 14.sp,
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建打字动画
  Widget _buildTypingAnimation() {
    return SizedBox(
      width: 40.w,
      height: 20.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          3,
          (index) => _buildTypingDot(index),
        ),
      ),
    );
  }

  /// 构建打字动画的点
  Widget _buildTypingDot(int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0, end: 1),
      duration: Duration(milliseconds: 300 + index * 200),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Opacity(
          opacity: (0.4 + 0.6 * (value > 0.5 ? 1 - value : value) * 2),
          child: Container(
            width: 6.w,
            height: 6.w,
            decoration: BoxDecoration(
              color: context.theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  //------------------------------- 输入处理方法 -------------------------------//

  /// 切换附件选项区域的显示状态
  void _toggleAttachmentOptions() {
    if (_isProcessingMedia) return;

    HapticFeedback.selectionClick();

    if (!_isAttachmentOptionsVisible) {
      final inputController =
          ref.read(chatInputControllerProvider(widget.conversationId));
      inputController.focusNode.unfocus();
    }

    setState(() {
      _isAttachmentOptionsVisible = !_isAttachmentOptionsVisible;
    });
  }

  /// 隐藏附件选项
  void _hideAttachmentOptions() {
    if (_isAttachmentOptionsVisible && !_isProcessingMedia) {
      setState(() {
        _isAttachmentOptionsVisible = false;
      });
    }
  }

  /// 处理发送聊天信息
  void _handleSendText(ChatInputController controller) {
    if (controller.getText().isNotEmpty) {
      HapticFeedback.mediumImpact();
      controller.sendText(widget.onSendText);
    }
  }

  /// 处理录音完成
  Future<void> _handleAudioRecordComplete(
      String path, Duration duration) async {
    try {
      LogUtils.d(
          'Processing audio record complete: path=$path, duration=$duration',
          tag: 'CustomChat');
      setState(() => _isProcessingMedia = true);

      // 检查原始录音文件
      final originalFile = File(path);
      if (await originalFile.exists()) {
        final originalSize = await originalFile.length();
        LogUtils.d('Original audio file exists, size: $originalSize bytes',
            tag: 'CustomChat');
      } else {
        LogUtils.e('Original audio file does not exist: $path',
            tag: 'CustomChat');
        LoadingUtils.showToast('录音文件不存在，请重试');
        return;
      }

      final fileService = getIt<file_service.IFileService>();
      final fileResult = await fileService.importFile(
        sourcePath: path,
        type: file_service.FileType.audio,
      );

      if (fileResult.isLeft()) {
        LogUtils.e('Failed to import audio file: ${fileResult.getLeft()}',
            tag: 'CustomChat');
        LoadingUtils.showToast('录音失败，请稍后重试');
        return;
      }

      final metadata = fileResult.getRight()!;
      LogUtils.d('Audio file imported successfully: ${metadata.toJson()}',
          tag: 'CustomChat');

      HapticFeedback.mediumImpact();
      widget.onSendAudio(metadata, duration);
    } catch (e) {
      LogUtils.e('Error processing audio: $e', tag: 'CustomChat');
      LoadingUtils.showToast('处理录音时出错，请稍后重试');
    } finally {
      setState(() {
        _isRecordingMode = false;
        _isProcessingMedia = false;
      });
    }
  }

  //------------------------------- 媒体处理方法 -------------------------------//

  /// 处理选择图片
  Future<void> _handlePickImage() async {
    if (_isProcessingMedia) return;

    try {
      setState(() => _isProcessingMedia = true);

      final fileMetadata =
          await _mediaService.pickAndProcessImage(widget.conversationId);
      if (fileMetadata != null) {
        HapticFeedback.mediumImpact();
        widget.onSendImage(fileMetadata);
      }
    } finally {
      setState(() => _isProcessingMedia = false);
    }
  }

  /// 处理选择视频
  Future<void> _handlePickVideo() async {
    if (_isProcessingMedia) return;

    try {
      setState(() => _isProcessingMedia = true);

      final (fileMetadata, duration) =
          await _mediaService.pickAndProcessVideo(widget.conversationId);
      if (fileMetadata != null && duration != null) {
        HapticFeedback.mediumImpact();
        widget.onSendVideo(fileMetadata, duration);
      }
    } finally {
      setState(() => _isProcessingMedia = false);
    }
  }

  Menu _buildContextMenu(types.Message message) {
    final List<MenuElement> menuItems = [];

    switch (message.type) {
      case types.MessageType.text:
        final textMessage = message as types.TextMessage;
        menuItems.addAll([
          MenuAction(
            title: 'Copy',
            callback: () {
              Clipboard.setData(ClipboardData(text: textMessage.text));
              LoadingUtils.showToast(
                'Copied to clipboard',
                maskType: EasyLoadingMaskType.clear,
              );
            },
          ),
        ]);
        break;
      case types.MessageType.image:
        break;
      case types.MessageType.video:
        break;
      default:
        break;
    }

    if (message.status == types.Status.error) {
      menuItems.add(
        MenuAction(
          title: 'Resend',
          callback: () {
            ref
                .read(chatProvider(widget.conversationId).notifier)
                .retryMessage(message);
          },
        ),
      );
    }

    menuItems.add(
      MenuAction(
        title: 'Delete',
        attributes: const MenuActionAttributes(
          destructive: true,
        ),
        callback: () async {
          final result = await context.showOkCancelAlertDialog(
            title: 'Delete Message',
            content: 'Are you sure you want to delete this message?',
          );
          if (result == true) {
            ref
                .read(chatProvider(widget.conversationId).notifier)
                .deleteMessage(message);
          }
        },
      ),
    );

    return Menu(children: menuItems);
  }

  void _onMessageStatusTap(BuildContext context, types.Message message) {
    if (message.status == types.Status.error &&
        message.type != types.MessageType.system) {
      showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            title: const Text('Failed to send message'),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  context.pop();
                  ref
                      .read(chatProvider(widget.conversationId).notifier)
                      .retryMessage(message);
                },
                child: const Text('Retry'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
          );
        },
      );
    }
  }
}

/// 聊天列表项类型
enum _ChatListItemType {
  message,
  dateHeader,
}

/// 聊天列表项模型
class _ChatListItem {
  final _ChatListItemType type;
  final types.Message message;
  final bool isNextInGroup;
  final int? timestamp;

  _ChatListItem({
    required this.type,
    required this.message,
    this.isNextInGroup = false,
    this.timestamp,
  });
}
