import 'dart:async';
import 'dart:io';

import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:just_audio/just_audio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'audio_player_controller.g.dart';

// Audio source type
enum AudioSourceType {
  remote, // Remote URL (http/https)
  file, // Local file path
  asset, // Asset path
}

// Audio player states
enum AudioPlayerStatus {
  initial,
  loading,
  playing,
  paused,
  completed,
  error,
}

// Audio source class
class AudioSource {
  final String id;
  final String path;
  final AudioSourceType type;

  const AudioSource({
    required this.id,
    required this.path,
    required this.type,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AudioSource &&
          runtimeType == other.runtimeType &&
          path == other.path &&
          type == other.type &&
          id == other.id;

  @override
  int get hashCode => path.hashCode ^ type.hashCode ^ id.hashCode;
}

// Audio player state class
class AudioPlayerState {
  final AudioPlayerStatus status;
  final AudioSource? currentAudio;
  final Duration? duration;
  final Duration? position;
  final String? error;

  const AudioPlayerState({
    this.status = AudioPlayerStatus.initial,
    this.currentAudio,
    this.duration,
    this.position,
    this.error,
  });

  AudioPlayerState copyWith({
    AudioPlayerStatus? status,
    AudioSource? currentAudio,
    Duration? duration,
    Duration? position,
    String? error,
  }) {
    return AudioPlayerState(
      status: status ?? this.status,
      currentAudio: currentAudio ?? this.currentAudio,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      error: error ?? this.error,
    );
  }
}

@riverpod
class AudioPlayerController extends _$AudioPlayerController {
  final _player = AudioPlayer();
  
  // 使用可空类型存储 StreamSubscription
  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  
  @override
  AudioPlayerState build() {
    _initializeListeners();
    ref.onDispose(_dispose);
    return const AudioPlayerState();
  }

  void _initializeListeners() {
    // 取消已存在的监听器
    _cancelExistingSubscriptions();

    // 初始化新的监听器
    _playerStateSubscription = _player.playerStateStream.listen(
      _handlePlayerStateChange,
      onError: _handlePlayerError,
    );

    _positionSubscription = _player.positionStream.listen(
      _handlePositionChange,
      onError: _handlePlayerError,
    );

    _durationSubscription = _player.durationStream.listen(
      _handleDurationChange,
      onError: _handlePlayerError,
    );
  }

  // 取消所有已存在的监听器
  void _cancelExistingSubscriptions() {
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();

    _playerStateSubscription = null;
    _positionSubscription = null;
    _durationSubscription = null;
  }

  // 集中处理播放器状态变化
  void _handlePlayerStateChange(PlayerState playerState) {
    LogUtils.d(
        'Player state changed: processingState=${playerState.processingState}, playing=${playerState.playing}',
        tag: 'AudioPlayerController');

    final newStatus = _mapProcessingStateToStatus(playerState.processingState);
    if (newStatus != null) {
      LogUtils.d('Updating status to: $newStatus',
          tag: 'AudioPlayerController');
      state = state.copyWith(
        status: newStatus,
        error: null, // 清除之前的错误状态
      );
    }
  }

  // 将 ProcessingState 映射到 AudioPlayerStatus
  AudioPlayerStatus? _mapProcessingStateToStatus(ProcessingState state) {
    switch (state) {
      case ProcessingState.loading:
        return AudioPlayerStatus.loading;
      case ProcessingState.ready:
        return _player.playing
            ? AudioPlayerStatus.playing
            : AudioPlayerStatus.paused;
      case ProcessingState.completed:
        return AudioPlayerStatus.completed;
      case ProcessingState.idle:
      case ProcessingState.buffering:
        return null; // 保持当前状态
    }
  }

  // 处理位置变化
  void _handlePositionChange(Duration position) {
    state = state.copyWith(position: position);
  }

  // 处理时长变化
  void _handleDurationChange(Duration? duration) {
    state = state.copyWith(duration: duration);
  }

  // 统一错误处理
  void _handlePlayerError(Object error) {
    LogUtils.e('Player error: $error', tag: 'AudioPlayerController');
    state = state.copyWith(
      status: AudioPlayerStatus.error,
      error: error.toString(),
    );
  }

  // 清理资源
  Future<void> _dispose() async {
    LogUtils.d('dispose audio player controller',
        tag: 'audio player controller');
    // 取消所有监听器
    _cancelExistingSubscriptions();

    // 停止并释放播放器
    await _player.stop();
    await _player.dispose();
  }

  // Set audio source based on type
  Future<void> _setAudioSource(AudioSource source) async {
    const maxRetries = 2;
    int currentTry = 0;

    LogUtils.d(
        'Setting audio source: type=${source.type}, path=${source.path}, id=${source.id}',
        tag: 'AudioPlayerController');

    while (currentTry <= maxRetries) {
      try {
        switch (source.type) {
          case AudioSourceType.remote:
            LogUtils.d('Setting remote URL: ${source.path}',
                tag: 'AudioPlayerController');
            await _player.setUrl(source.path);
            break;
          case AudioSourceType.file:
            LogUtils.d('Setting file path: ${source.path}',
                tag: 'AudioPlayerController');
            // 检查文件是否存在
            final file = File(source.path);
            if (!await file.exists()) {
              throw Exception('Audio file does not exist: ${source.path}');
            }
            final fileSize = await file.length();
            LogUtils.d('Audio file exists, size: $fileSize bytes',
                tag: 'AudioPlayerController');
            await _player.setFilePath(source.path);
            break;
          case AudioSourceType.asset:
            LogUtils.d('Setting asset path: ${source.path}',
                tag: 'AudioPlayerController');
            await _player.setAsset(source.path);
            break;
        }

        // 获取音频时长信息
        final duration = _player.duration;
        LogUtils.d('Audio source set successfully, duration: $duration',
            tag: 'AudioPlayerController');

        // 成功设置音频源，退出重试循环
        return;
      } catch (e) {
        currentTry++;
        LogUtils.e('设置音频源失败 (尝试 $currentTry/$maxRetries): $e',
            tag: 'AudioPlayerController');

        // 最后一次尝试失败，抛出异常
        if (currentTry > maxRetries) {
          rethrow;
        }

        // 在重试前重置播放器
        try {
          await _player.stop();
          // 短暂延迟后重试
          await Future.delayed(const Duration(milliseconds: 300));
        } catch (_) {
          // 忽略重置过程中的错误
        }
      }
    }
  }

  // Play audio from URL
  Future<void> playAudio(
    String path, {
    AudioSourceType type = AudioSourceType.remote,
    required String id,
  }) async {
    try {
      final audioSource = AudioSource(
        path: path,
        type: type,
        id: id,
      );

      // 如果相同的音频在暂停状态，只需恢复播放
      if (state.currentAudio == audioSource &&
          state.status == AudioPlayerStatus.paused) {
        await _player.play();
        return;
      }

      // 重置状态并停止当前播放
      await stop();
      
      state = state.copyWith(
        status: AudioPlayerStatus.loading,
        currentAudio: audioSource,
      );

      // 设置新的音频源并播放
      await _setAudioSource(audioSource);
      await _player.play();
    } catch (e) {
      LogUtils.e('Failed to play audio: $e', tag: 'AudioPlayerController');
      // 发生错误时重置状态
      state = state.copyWith(
        status: AudioPlayerStatus.error,
        error: e.toString(),
      );
      
      // 错误时尝试重置播放器
      try {
        await _player.stop();
      } catch (_) {
        // 忽略可能的错误
      }
    }
  }

  // Pause current playback
  Future<void> pause() async {
    await _player.pause();
  }

  // Stop and reset player
  Future<void> stop() async {
    try {
      // 停止播放
      await _player.stop();
      
      // 重置内部状态
      state = const AudioPlayerState();
      
      // 尝试清除缓存
      try {
        await _player.setAudioSource(ConcatenatingAudioSource(children: []));
      } catch (_) {
        // 忽略设置空源可能出现的错误
      }
    } catch (e) {
      LogUtils.e('Failed to stop audio: $e', tag: 'AudioPlayerController');
    }
  }

  // Seek to position
  Future<void> seekTo(Duration position) async {
    await _player.seek(position);
  }
}
