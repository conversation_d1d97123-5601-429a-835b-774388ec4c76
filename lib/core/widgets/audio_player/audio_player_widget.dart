import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'audio_player_controller.dart';

class AudioPlayerWidget extends ConsumerWidget {
  final String id;
  final String audioPath;
  final AudioSourceType sourceType;
  final bool showProgress;
  final bool autoPlay;
  final Widget? playIcon;
  final Widget? pauseIcon;
  final Widget? loadingWidget;
  final VoidCallback? onPlayComplete;
  final double? width;
  final double? height;

  const AudioPlayerWidget({
    super.key,
    required this.id,
    required this.audioPath,
    this.sourceType = AudioSourceType.remote,
    this.showProgress = true,
    this.autoPlay = false,
    this.playIcon,
    this.pauseIcon,
    this.loadingWidget,
    this.onPlayComplete,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final playerState = ref.watch(audioPlayerControllerProvider);
    final controller = ref.read(audioPlayerControllerProvider.notifier);

    final currentAudio = AudioSource(
      path: audioPath,
      type: sourceType,
      id: id,
    );
    final isCurrentAudio = playerState.currentAudio == currentAudio;
    final isPlaying =
        isCurrentAudio && playerState.status == AudioPlayerStatus.playing;

    // Handle completion callback
    if (isCurrentAudio && playerState.status == AudioPlayerStatus.completed) {
      onPlayComplete?.call();
    }

    return SizedBox(
      width: width,
      height: height,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Play/Pause button
          GestureDetector(
            onTap: () {
              if (isPlaying) {
                controller.pause();
              } else {
                controller.playAudio(audioPath, type: sourceType, id: id);
              }
            },
            child: _buildPlayPauseIcon(playerState, isCurrentAudio, isPlaying),
          ),

          // Progress bar
          if (showProgress && isCurrentAudio) ...[
            const SizedBox(width: 8),
            Expanded(
              child: _buildProgressBar(playerState, controller, context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlayPauseIcon(
    AudioPlayerState state,
    bool isCurrentAudio,
    bool isPlaying,
  ) {
    Widget icon;
    if (isCurrentAudio && state.status == AudioPlayerStatus.loading) {
      icon = loadingWidget ?? const CircularProgressIndicator(strokeWidth: 2);
    }

    if (isPlaying) {
      icon = pauseIcon ?? const Icon(Icons.pause);
    }

    icon = playIcon ?? const Icon(Icons.play_arrow);

    return SizedBox(
      width: height,
      height: height,
      child: icon,
    );
  }

  Widget _buildProgressBar(
    AudioPlayerState state,
    AudioPlayerController controller,
    BuildContext context,
  ) {
    final duration = state.duration ?? Duration.zero;
    final position = state.position ?? Duration.zero;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SliderTheme(
          data: const SliderThemeData(
            trackHeight: 2,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 6),
          ),
          child: Slider(
            value: position.inMilliseconds.toDouble(),
            max: duration.inMilliseconds.toDouble(),
            onChanged: (value) {
              controller.seekTo(Duration(milliseconds: value.toInt()));
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(position),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                _formatDuration(duration),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
