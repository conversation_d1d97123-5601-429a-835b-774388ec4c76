import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'audio_player_controller.dart';

/// Audio player widget with customizable UI
class AudioPlayerWidget extends ConsumerStatefulWidget {
  final String id;
  final String audioPath;
  final AudioSourceType sourceType;
  final bool showProgress;
  final bool autoPlay;
  final Widget? playIcon;
  final Widget? pauseIcon;
  final Widget? loadingWidget;
  final VoidCallback? onPlayComplete;
  final double? width;
  final double? height;

  // Custom UI builder
  final Widget Function(
      BuildContext context,
      AudioPlayerState state,
      bool isCurrentAudio,
      bool isPlaying,
      VoidCallback onPlayPause)? customUIBuilder;

  // Default UI style options
  final bool useDefaultUI;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;
  final bool showWaveform;
  final bool showDuration;

  const AudioPlayerWidget({
    super.key,
    required this.id,
    required this.audioPath,
    this.sourceType = AudioSourceType.remote,
    this.showProgress = true,
    this.autoPlay = false,
    this.playIcon,
    this.pauseIcon,
    this.loadingWidget,
    this.onPlayComplete,
    this.width,
    this.height,
    this.customUIBuilder,
    this.useDefaultUI = false,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.showWaveform = true,
    this.showDuration = true,
  });

  /// Factory constructor for default UI style
  const AudioPlayerWidget.defaultStyle({
    super.key,
    required this.id,
    required this.audioPath,
    this.sourceType = AudioSourceType.remote,
    this.autoPlay = false,
    this.onPlayComplete,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.showWaveform = true,
    this.showDuration = true,
  })  : showProgress = true,
        playIcon = null,
        pauseIcon = null,
        loadingWidget = null,
        width = null,
        height = null,
        customUIBuilder = null,
        useDefaultUI = true;

  @override
  ConsumerState<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends ConsumerState<AudioPlayerWidget> {
  bool _hasAutoPlayed = false;

  @override
  void initState() {
    super.initState();
    // Handle auto play in initState instead of build
    if (widget.autoPlay) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleAutoPlay();
      });
    }
  }

  void _handleAutoPlay() {
    final playerState = ref.read(audioPlayerControllerProvider);
    final controller = ref.read(audioPlayerControllerProvider.notifier);

    final currentAudio = AudioSource(
      path: widget.audioPath,
      type: widget.sourceType,
      id: widget.id,
    );

    // Only auto play if not already played and no audio is currently playing
    if (!_hasAutoPlayed &&
        playerState.currentAudio != currentAudio &&
        playerState.status == AudioPlayerStatus.initial) {
      _hasAutoPlayed = true;
      controller.playAudio(widget.audioPath,
          type: widget.sourceType, id: widget.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final playerState = ref.watch(audioPlayerControllerProvider);
    final controller = ref.read(audioPlayerControllerProvider.notifier);

    final currentAudio = AudioSource(
      path: widget.audioPath,
      type: widget.sourceType,
      id: widget.id,
    );
    final isCurrentAudio = playerState.currentAudio == currentAudio;
    final isPlaying =
        isCurrentAudio && playerState.status == AudioPlayerStatus.playing;

    // Handle completion callback
    if (isCurrentAudio && playerState.status == AudioPlayerStatus.completed) {
      widget.onPlayComplete?.call();
    }

    onPlayPause() {
      if (isPlaying) {
        controller.pause();
      } else {
        controller.playAudio(widget.audioPath,
            type: widget.sourceType, id: widget.id);
      }
    }

    // Use custom UI builder if provided
    if (widget.customUIBuilder != null) {
      return widget.customUIBuilder!(
          context, playerState, isCurrentAudio, isPlaying, onPlayPause);
    }

    // Use default UI style if specified
    if (widget.useDefaultUI) {
      return _buildDefaultUI(context, playerState, isCurrentAudio, isPlaying,
          onPlayPause, controller);
    }

    // Use original simple UI
    return _buildSimpleUI(context, playerState, isCurrentAudio, isPlaying,
        onPlayPause, controller);
  }

  /// Build simple UI (original style)
  Widget _buildSimpleUI(
    BuildContext context,
    AudioPlayerState playerState,
    bool isCurrentAudio,
    bool isPlaying,
    VoidCallback onPlayPause,
    AudioPlayerController controller,
  ) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Play/Pause button
          GestureDetector(
            onTap: onPlayPause,
            child: _buildPlayPauseIcon(playerState, isCurrentAudio, isPlaying),
          ),

          // Progress bar
          if (widget.showProgress && isCurrentAudio) ...[
            const SizedBox(width: 8),
            Expanded(
              child: _buildProgressBar(playerState, controller, context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlayPauseIcon(
    AudioPlayerState state,
    bool isCurrentAudio,
    bool isPlaying,
  ) {
    Widget icon;

    // Fix: Check loading state first
    if (isCurrentAudio && state.status == AudioPlayerStatus.loading) {
      icon = widget.loadingWidget ??
          const CircularProgressIndicator(strokeWidth: 2);
    } else if (isPlaying) {
      // Fix: Add else if to properly handle playing state
      icon = widget.pauseIcon ?? const Icon(Icons.pause);
    } else {
      // Fix: Default to play icon when not playing
      icon = widget.playIcon ?? const Icon(Icons.play_arrow);
    }

    return SizedBox(
      width: widget.height,
      height: widget.height,
      child: icon,
    );
  }

  Widget _buildProgressBar(
    AudioPlayerState state,
    AudioPlayerController controller,
    BuildContext context,
  ) {
    final duration = state.duration ?? Duration.zero;
    final position = state.position ?? Duration.zero;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SliderTheme(
          data: const SliderThemeData(
            trackHeight: 2,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 6),
          ),
          child: Slider(
            value: position.inMilliseconds.toDouble(),
            max: duration.inMilliseconds.toDouble(),
            onChanged: (value) {
              controller.seekTo(Duration(milliseconds: value.toInt()));
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(position),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                _formatDuration(duration),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build default UI (voice message style)
  Widget _buildDefaultUI(
    BuildContext context,
    AudioPlayerState playerState,
    bool isCurrentAudio,
    bool isPlaying,
    VoidCallback onPlayPause,
    AudioPlayerController controller,
  ) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor =
        widget.backgroundColor ?? theme.colorScheme.surfaceContainerHighest;
    final effectiveForegroundColor =
        widget.foregroundColor ?? theme.colorScheme.onSurface;
    final effectiveBorderRadius = widget.borderRadius ?? 12.0;
    final effectivePadding = widget.padding ??
        const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);

    // Calculate width based on duration if available
    final duration = playerState.duration ?? Duration.zero;
    const minWidth = 120.0;
    final maxWidth = MediaQuery.of(context).size.width * 0.7;

    double audioWidth = minWidth;
    if (duration.inSeconds > 0) {
      audioWidth = minWidth + (duration.inSeconds * 8.0);
      audioWidth = audioWidth.clamp(minWidth, maxWidth);
    }

    return Container(
      constraints: BoxConstraints(
        minWidth: minWidth,
        maxWidth: maxWidth,
      ),
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Play/Pause button
          GestureDetector(
            onTap: onPlayPause,
            child: Container(
              width: 32.0,
              height: 32.0,
              decoration: BoxDecoration(
                color: effectiveForegroundColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: _buildDefaultPlayPauseIcon(playerState, isCurrentAudio,
                  isPlaying, effectiveForegroundColor),
            ),
          ),

          const SizedBox(width: 12),

          // Waveform and duration
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Waveform or progress indicator
                if (widget.showWaveform) ...[
                  _buildWaveform(context, playerState, isCurrentAudio,
                      isPlaying, effectiveForegroundColor),
                  const SizedBox(height: 4),
                ],

                // Duration and progress
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (widget.showDuration) ...[
                      Text(
                        isCurrentAudio && playerState.position != null
                            ? _formatDuration(playerState.position!)
                            : _formatDuration(duration),
                        style: TextStyle(
                          color:
                              effectiveForegroundColor.withValues(alpha: 0.7),
                          fontSize: 12.0,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (isCurrentAudio && playerState.duration != null)
                        Text(
                          _formatDuration(playerState.duration!),
                          style: TextStyle(
                            color:
                                effectiveForegroundColor.withValues(alpha: 0.5),
                            fontSize: 12.0,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultPlayPauseIcon(
    AudioPlayerState state,
    bool isCurrentAudio,
    bool isPlaying,
    Color color,
  ) {
    Widget icon;

    if (isCurrentAudio && state.status == AudioPlayerStatus.loading) {
      icon = SizedBox(
        width: 18.0,
        height: 18.0,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: color,
        ),
      );
    } else if (isPlaying) {
      icon = Icon(
        Icons.pause_rounded,
        color: color,
        size: 18.0,
      );
    } else {
      icon = Icon(
        Icons.play_arrow_rounded,
        color: color,
        size: 18.0,
      );
    }

    return icon;
  }

  Widget _buildWaveform(
    BuildContext context,
    AudioPlayerState playerState,
    bool isCurrentAudio,
    bool isPlaying,
    Color color,
  ) {
    return Row(
      children: List.generate(
        20,
        (index) {
          // Create animated waveform effect when playing
          final baseHeight = 8.0 + (index % 3) * 4.0;
          final animatedHeight = isCurrentAudio && isPlaying
              ? baseHeight *
                  (0.5 +
                      0.5 *
                          ((index +
                                  DateTime.now().millisecondsSinceEpoch ~/
                                      200) %
                              3) /
                          2)
              : baseHeight;

          return Container(
            width: 2.0,
            height: animatedHeight,
            margin: const EdgeInsets.only(right: 1.0),
            decoration: BoxDecoration(
              color: isCurrentAudio && isPlaying
                  ? color.withValues(alpha: 0.8)
                  : color.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(1.0),
            ),
          );
        },
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
